import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';

// Create a custom render function that includes providers
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div>
      {/* Add any providers here (Router, Theme, etc.) */}
      {children}
    </div>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Helper functions for common test scenarios
export const mockConsole = () => {
  const originalConsole = { ...console };
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
  
  return () => {
    Object.assign(console, originalConsole);
  };
};

export const createMockTrade = (overrides = {}) => ({
  id: '1',
  symbol: 'AAPL',
  entryPrice: 150,
  currentPrice: 155,
  quantity: 100,
  direction: 'long',
  ...overrides
});

export const createMockTradingPlan = (overrides = {}) => ({
  symbol: 'AAPL',
  direction: 'long',
  entryPrice: 150,
  stopLoss: 145,
  takeProfit: 160,
  quantity: 100,
  reasoning: 'Test reasoning',
  keyLevels: [150, 155, 160],
  riskReward: 2,
  ...overrides
});