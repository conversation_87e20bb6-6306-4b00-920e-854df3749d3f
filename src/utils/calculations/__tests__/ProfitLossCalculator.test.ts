import {
  calculateProfitLoss,
  formatProfitLoss,
  calculatePortfolioProfitLoss
} from '../ProfitLossCalculator';

describe('ProfitLossCalculator', () => {
  describe('calculateProfitLoss', () => {
    it('should calculate profit for long positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 110,
        quantity: 10,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (110 - 100) * 10
      expect(result.percentage).toBe(10); // 10% gain
      expect(result.isPositive).toBe(true);
    });

    it('should calculate loss for long positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 90,
        quantity: 10,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (100 - 90) * 10
      expect(result.percentage).toBe(10); // 10% loss
      expect(result.isPositive).toBe(false);
    });

    it('should calculate profit for short positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 90,
        quantity: 10,
        direction: 'short' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (100 - 90) * 10
      expect(result.percentage).toBe(10); // 10% gain
      expect(result.isPositive).toBe(true);
    });

    it('should calculate loss for short positions correctly', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 110,
        quantity: 10,
        direction: 'short' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100); // (110 - 100) * 10
      expect(result.percentage).toBe(10); // 10% loss
      expect(result.isPositive).toBe(false);
    });

    it('should handle zero profit/loss', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 100,
        quantity: 10,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(0);
      expect(result.percentage).toBe(0);
      expect(result.isPositive).toBe(true); // Zero is considered positive
    });

    it('should handle fractional shares', () => {
      const trade = {
        entryPrice: 100,
        currentPrice: 105,
        quantity: 1.5,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(7.5); // (105 - 100) * 1.5
      expect(result.percentage).toBe(5); // 5% gain
      expect(result.isPositive).toBe(true);
    });
  });

  describe('formatProfitLoss', () => {
    it('should format positive values correctly', () => {
      const result = formatProfitLoss(123.45, true);
      expect(result).toBe('+$123.45');
    });

    it('should format negative values correctly', () => {
      const result = formatProfitLoss(123.45, false);
      expect(result).toBe('-$123.45');
    });

    it('should handle zero values', () => {
      const result = formatProfitLoss(0, true);
      expect(result).toBe('+$0.00');
    });

    it('should round to 2 decimal places', () => {
      const result = formatProfitLoss(123.456789, true);
      expect(result).toBe('+$123.46');
    });
  });

  describe('calculatePortfolioProfitLoss', () => {
    it('should calculate total portfolio P&L correctly', () => {
      const trades = [
        {
          entryPrice: 100,
          currentPrice: 110,
          quantity: 10,
          direction: 'long' as const
        },
        {
          entryPrice: 200,
          currentPrice: 180,
          quantity: 5,
          direction: 'long' as const
        }
      ];
      
      const result = calculatePortfolioProfitLoss(trades);
      
      // Trade 1: +100, Trade 2: -100, Total: 0
      expect(result.absoluteValue).toBe(0);
      expect(result.percentage).toBe(0);
      expect(result.isPositive).toBe(true);
    });

    it('should handle empty portfolio', () => {
      const result = calculatePortfolioProfitLoss([]);
      
      expect(result.absoluteValue).toBe(0);
      expect(result.percentage).toBe(0);
      expect(result.isPositive).toBe(true);
    });

    it('should handle mixed long and short positions', () => {
      const trades = [
        {
          entryPrice: 100,
          currentPrice: 110,
          quantity: 10,
          direction: 'long' as const
        },
        {
          entryPrice: 200,
          currentPrice: 210,
          quantity: 5,
          direction: 'short' as const
        }
      ];
      
      const result = calculatePortfolioProfitLoss(trades);
      
      // Trade 1: +100, Trade 2: -50, Total: +50
      expect(result.absoluteValue).toBe(50);
      expect(result.isPositive).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle very large numbers', () => {
      const trade = {
        entryPrice: 1000000,
        currentPrice: 1100000,
        quantity: 1000,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBe(100000000);
      expect(result.percentage).toBe(10);
      expect(result.isPositive).toBe(true);
    });

    it('should handle very small numbers', () => {
      const trade = {
        entryPrice: 0.001,
        currentPrice: 0.0011,
        quantity: 1000,
        direction: 'long' as const
      };
      
      const result = calculateProfitLoss(trade);
      
      expect(result.absoluteValue).toBeCloseTo(0.1);
      expect(result.percentage).toBeCloseTo(10);
      expect(result.isPositive).toBe(true);
    });
  });
});