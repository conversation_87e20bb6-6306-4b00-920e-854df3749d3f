/**
 * Utility functions for profit/loss calculations
 * Extracted from ProfitLossCell component
 */

export interface TradeData {
  entryPrice: number;
  currentPrice: number;
  quantity: number;
  direction: 'long' | 'short';
}

export interface ProfitLossResult {
  absoluteValue: number;
  percentage: number;
  isPositive: boolean;
}

/**
 * Calculate profit/loss for a trade
 */
export const calculateProfitLoss = (trade: TradeData): ProfitLossResult => {
  const { entryPrice, currentPrice, quantity, direction } = trade;

  let absoluteValue: number;

  if (direction === 'long') {
    absoluteValue = (currentPrice - entryPrice) * quantity;
  } else {
    absoluteValue = (entryPrice - currentPrice) * quantity;
  }

  const percentage = (absoluteValue / (entryPrice * quantity)) * 100;
  const isPositive = absoluteValue >= 0;

  return {
    absoluteValue: Math.abs(absoluteValue),
    percentage: Math.abs(percentage),
    isPositive
  };
};

/**
 * Format profit/loss value for display
 */
export const formatProfitLoss = (value: number, isPositive: boolean): string => {
  const prefix = isPositive ? '+' : '-';
  return `${prefix}$${value.toFixed(2)}`;
};

/**
 * Calculate portfolio-level profit/loss
 */
export const calculatePortfolioProfitLoss = (trades: TradeData[]): ProfitLossResult => {
  const totalPnL = trades.reduce((sum, trade) => {
    const result = calculateProfitLoss(trade);
    return sum + (result.isPositive ? result.absoluteValue : -result.absoluteValue);
  }, 0);

  const totalValue = trades.reduce((sum, trade) => {
    return sum + (trade.entryPrice * trade.quantity);
  }, 0);

  const percentage = totalValue > 0 ? (totalPnL / totalValue) * 100 : 0;

  return {
    absoluteValue: Math.abs(totalPnL),
    percentage: Math.abs(percentage),
    isPositive: totalPnL >= 0
  };
};