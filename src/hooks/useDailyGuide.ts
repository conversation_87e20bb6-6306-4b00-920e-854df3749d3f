import { useState, useEffect, useCallback, createContext, useContext } from 'react';

export interface DailyGuideData {
  currentGuide: any | null;
  guides: any[];
  isLoading: boolean;
  error: string | null;
  currentDate: Date;
}

export interface UseDailyGuideReturn {
  data: DailyGuideData;
  actions: {
    loadGuide: (date: Date) => Promise<void>;
    createGuide: (guideData: any) => Promise<void>;
    updateGuide: (id: string, updates: any) => Promise<void>;
    deleteGuide: (id: string) => Promise<void>;
    setCurrentDate: (date: Date) => void;
    refreshGuides: () => Promise<void>;
  };
}

/**
 * Custom hook for managing daily guide functionality
 * Converted from DailyGuideContext for better hook-based architecture
 */
export const useDailyGuide = (): UseDailyGuideReturn => {
  const [data, setData] = useState<DailyGuideData>({
    currentGuide: null,
    guides: [],
    isLoading: false,
    error: null,
    currentDate: new Date()
  });

  const loadGuide = useCallback(async (date: Date) => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const guide = await dailyGuideApi.getGuideByDate(date);
      const guide = null; // Placeholder

      setData(prev => ({
        ...prev,
        currentGuide: guide,
        currentDate: date,
        isLoading: false
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load guide'
      }));
    }
  }, []);

  const createGuide = useCallback(async (guideData: any) => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const newGuide = await dailyGuideApi.createGuide(guideData);
      const newGuide = { ...guideData, id: Date.now().toString() }; // Placeholder

      setData(prev => ({
        ...prev,
        currentGuide: newGuide,
        guides: [...prev.guides, newGuide],
        isLoading: false
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create guide'
      }));
    }
  }, []);

  const updateGuide = useCallback(async (id: string, updates: any) => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const updatedGuide = await dailyGuideApi.updateGuide(id, updates);
      const updatedGuide = { ...updates, id }; // Placeholder

      setData(prev => ({
        ...prev,
        currentGuide: prev.currentGuide?.id === id ? updatedGuide : prev.currentGuide,
        guides: prev.guides.map(guide => guide.id === id ? updatedGuide : guide),
        isLoading: false
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update guide'
      }));
    }
  }, []);

  const deleteGuide = useCallback(async (id: string) => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // await dailyGuideApi.deleteGuide(id);

      setData(prev => ({
        ...prev,
        currentGuide: prev.currentGuide?.id === id ? null : prev.currentGuide,
        guides: prev.guides.filter(guide => guide.id !== id),
        isLoading: false
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete guide'
      }));
    }
  }, []);

  const setCurrentDate = useCallback((date: Date) => {
    setData(prev => ({ ...prev, currentDate: date }));
    loadGuide(date);
  }, [loadGuide]);

  const refreshGuides = useCallback(async () => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const guides = await dailyGuideApi.getAllGuides();
      const guides = []; // Placeholder

      setData(prev => ({
        ...prev,
        guides,
        isLoading: false
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to refresh guides'
      }));
    }
  }, []);

  // Load initial guide for current date
  useEffect(() => {
    loadGuide(data.currentDate);
  }, []);

  const actions = {
    loadGuide,
    createGuide,
    updateGuide,
    deleteGuide,
    setCurrentDate,
    refreshGuides
  };

  return {
    data,
    actions
  };
};

// Context for backwards compatibility (optional)
const DailyGuideContext = createContext<UseDailyGuideReturn | undefined>(undefined);

export const DailyGuideProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const value = useDailyGuide();
  return <DailyGuideContext.Provider value={value}>{children}</DailyGuideContext.Provider>;
};

export const useDailyGuideContext = () => {
  const context = useContext(DailyGuideContext);
  if (context === undefined) {
    throw new Error('useDailyGuideContext must be used within a DailyGuideProvider');
  }
  return context;
};