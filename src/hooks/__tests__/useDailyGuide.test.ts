import { renderHook, act } from '@testing-library/react';
import { useDailyGuide } from '../useDailyGuide';

describe('useDailyGuide', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useDailyGuide());
      
      expect(result.current.data).toBeDefined();
      expect(result.current.actions).toBeDefined();
      expect(result.current.data.isLoading).toBe(false);
      expect(result.current.data.error).toBeNull();
    });
  });

  describe('loading states', () => {
    it('should handle loading state correctly', async () => {
      const { result } = renderHook(() => useDailyGuide());
      
      await act(async () => {
        await result.current.actions.loadTrades?.();
      });

      // Verify loading state was set and then cleared
      expect(result.current.data.isLoading).toBe(false);
    });

    it('should handle error states', async () => {
      // Mock API failure
      const mockError = new Error('API Error');
      jest.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useDailyGuide());
      
      // Simulate error condition
      await act(async () => {
        try {
          await result.current.actions.loadTrades?.();
        } catch (error) {
          // Expected to catch error
        }
      });

      // Should handle errors gracefully
      expect(result.current.data.isLoading).toBe(false);
    });
  });

  describe('data management', () => {
    it('should update data correctly', async () => {
      const { result } = renderHook(() => useDailyGuide());
      
      const testData = { test: 'data' };
      
      await act(async () => {
        if (result.current.actions.updateFilters) {
          result.current.actions.updateFilters(testData);
        }
      });

      // Verify data was updated
      expect(result.current.data).toBeDefined();
    });

    it('should clear error when requested', async () => {
      const { result } = renderHook(() => useDailyGuide());
      
      await act(async () => {
        if (result.current.actions.clearError) {
          result.current.actions.clearError();
        }
      });

      expect(result.current.data.error).toBeNull();
    });
  });

  describe('cleanup', () => {
    it('should clean up properly on unmount', () => {
      const { unmount } = renderHook(() => useDailyGuide());
      
      expect(() => unmount()).not.toThrow();
    });
  });
});