import { useState, useEffect, useCallback, useMemo } from 'react';

export interface TradingDashboardData {
  trades: any[];
  isLoading: boolean;
  error: string | null;
  filters: {
    dateRange: [Date, Date] | null;
    symbol: string;
    status: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

export interface UseTradingDashboardDataReturn {
  data: TradingDashboardData;
  actions: {
    loadTrades: () => Promise<void>;
    updateFilters: (filters: Partial<TradingDashboardData['filters']>) => void;
    updatePagination: (pagination: Partial<TradingDashboardData['pagination']>) => void;
    refreshData: () => Promise<void>;
    clearError: () => void;
  };
}

/**
 * Custom hook for managing trading dashboard data and state
 * Extracted from TradingDashboard component for better reusability and testability
 */
export const useTradingDashboardData = (): UseTradingDashboardDataReturn => {
  const [data, setData] = useState<TradingDashboardData>({
    trades: [],
    isLoading: false,
    error: null,
    filters: {
      dateRange: null,
      symbol: '',
      status: 'all'
    },
    pagination: {
      page: 1,
      limit: 20,
      total: 0
    }
  });

  const loadTrades = useCallback(async () => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Replace with actual API call
      // const trades = await tradingApi.getTrades(data.filters, data.pagination);
      const trades = []; // Placeholder

      setData(prev => ({
        ...prev,
        trades,
        isLoading: false,
        pagination: {
          ...prev.pagination,
          total: trades.length
        }
      }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load trades'
      }));
    }
  }, [data.filters, data.pagination]);

  const updateFilters = useCallback((newFilters: Partial<TradingDashboardData['filters']>) => {
    setData(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters },
      pagination: { ...prev.pagination, page: 1 } // Reset to first page
    }));
  }, []);

  const updatePagination = useCallback((newPagination: Partial<TradingDashboardData['pagination']>) => {
    setData(prev => ({
      ...prev,
      pagination: { ...prev.pagination, ...newPagination }
    }));
  }, []);

  const refreshData = useCallback(async () => {
    await loadTrades();
  }, [loadTrades]);

  const clearError = useCallback(() => {
    setData(prev => ({ ...prev, error: null }));
  }, []);

  // Load initial data
  useEffect(() => {
    loadTrades();
  }, [data.filters, data.pagination.page, data.pagination.limit]);

  const actions = useMemo(() => ({
    loadTrades,
    updateFilters,
    updatePagination,
    refreshData,
    clearError
  }), [loadTrades, updateFilters, updatePagination, refreshData, clearError]);

  return {
    data,
    actions
  };
};