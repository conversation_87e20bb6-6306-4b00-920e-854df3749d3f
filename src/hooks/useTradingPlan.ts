import React from 'react';

interface TradingPlanViewProps {
  plan: any;
  errors: Record<string, string>;
  isValid: boolean;
  onUpdatePlan: (updates: any) => void;
  onSave: () => void;
  onCancel: () => void;
  className?: string;
}

/**
 * Pure UI component for trading plan form
 * Extracted from TradingPlan component for better testability
 */
export const TradingPlanView: React.FC<TradingPlanViewProps> = ({
  plan,
  errors,
  isValid,
  onUpdatePlan,
  onSave,
  onCancel,
  className = ''
}) => {
  return (
    <div className={`trading-plan-form ${className}`}>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Symbol
          </label>
          <input
            type="text"
            value={plan.symbol}
            onChange={(e) => onUpdatePlan({ symbol: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
            placeholder="e.g., AAPL"
          />
          {errors.symbol && (
            <p className="text-red-500 text-xs mt-1">{errors.symbol}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Direction
          </label>
          <select
            value={plan.direction}
            onChange={(e) => onUpdatePlan({ direction: e.target.value })}
            className="w-full px-3 py-2 border rounded-md"
          >
            <option value="long">Long</option>
            <option value="short">Short</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Entry Price
          </label>
          <input
            type="number"
            value={plan.entryPrice}
            onChange={(e) => onUpdatePlan({ entryPrice: parseFloat(e.target.value) || 0 })}
            className="w-full px-3 py-2 border rounded-md"
            step="0.01"
          />
          {errors.entryPrice && (
            <p className="text-red-500 text-xs mt-1">{errors.entryPrice}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Quantity
          </label>
          <input
            type="number"
            value={plan.quantity}
            onChange={(e) => onUpdatePlan({ quantity: parseInt(e.target.value) || 0 })}
            className="w-full px-3 py-2 border rounded-md"
          />
          {errors.quantity && (
            <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Stop Loss
          </label>
          <input
            type="number"
            value={plan.stopLoss}
            onChange={(e) => onUpdatePlan({ stopLoss: parseFloat(e.target.value) || 0 })}
            className="w-full px-3 py-2 border rounded-md"
            step="0.01"
          />
          {errors.stopLoss && (
            <p className="text-red-500 text-xs mt-1">{errors.stopLoss}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Take Profit
          </label>
          <input
            type="number"
            value={plan.takeProfit}
            onChange={(e) => onUpdatePlan({ takeProfit: parseFloat(e.target.value) || 0 })}
            className="w-full px-3 py-2 border rounded-md"
            step="0.01"
          />
          {errors.takeProfit && (
            <p className="text-red-500 text-xs mt-1">{errors.takeProfit}</p>
          )}
        </div>
      </div>

      <div className="mt-4">
        <label className="block text-sm font-medium mb-1">
          Reasoning
        </label>
        <textarea
          value={plan.reasoning}
          onChange={(e) => onUpdatePlan({ reasoning: e.target.value })}
          className="w-full px-3 py-2 border rounded-md"
          rows={4}
          placeholder="Explain your trading thesis..."
        />
      </div>

      {plan.riskReward > 0 && (
        <div className="mt-4 p-3 bg-gray-50 rounded-md">
          <p className="text-sm">
            Risk/Reward Ratio: <span className="font-semibold">{plan.riskReward.toFixed(2)}</span>
          </p>
        </div>
      )}

      <div className="flex justify-end space-x-3 mt-6">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={onSave}
          disabled={!isValid}
          className={`px-4 py-2 text-white rounded-md ${
            isValid
              ? 'bg-blue-600 hover:bg-blue-700'
              : 'bg-gray-400 cursor-not-allowed'
          }`}
        >
          Save Plan
        </button>
      </div>
    </div>
  );
};