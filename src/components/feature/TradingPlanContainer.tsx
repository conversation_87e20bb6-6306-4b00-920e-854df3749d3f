import { useState, useEffect, useCallback } from 'react';

export interface TradingPlanData {
  symbol: string;
  direction: 'long' | 'short';
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  quantity: number;
  reasoning: string;
  keyLevels: number[];
  riskReward: number;
}

export interface UseTradingPlanReturn {
  plan: TradingPlanData;
  isValid: boolean;
  errors: Record<string, string>;
  actions: {
    updatePlan: (updates: Partial<TradingPlanData>) => void;
    validatePlan: () => boolean;
    resetPlan: () => void;
    calculateRisk: () => number;
  };
}

/**
 * Custom hook for managing trading plan state and validation
 * Extracted from TradingPlan component
 */
export const useTradingPlan = (initialPlan?: Partial<TradingPlanData>): UseTradingPlanReturn => {
  const [plan, setPlan] = useState<TradingPlanData>({
    symbol: '',
    direction: 'long',
    entryPrice: 0,
    stopLoss: 0,
    takeProfit: 0,
    quantity: 0,
    reasoning: '',
    keyLevels: [],
    riskReward: 0,
    ...initialPlan
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validatePlan = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!plan.symbol) {
      newErrors.symbol = 'Symbol is required';
    }

    if (plan.entryPrice <= 0) {
      newErrors.entryPrice = 'Entry price must be positive';
    }

    if (plan.quantity <= 0) {
      newErrors.quantity = 'Quantity must be positive';
    }

    if (plan.direction === 'long') {
      if (plan.stopLoss >= plan.entryPrice) {
        newErrors.stopLoss = 'Stop loss must be below entry price for long positions';
      }
      if (plan.takeProfit <= plan.entryPrice) {
        newErrors.takeProfit = 'Take profit must be above entry price for long positions';
      }
    } else {
      if (plan.stopLoss <= plan.entryPrice) {
        newErrors.stopLoss = 'Stop loss must be above entry price for short positions';
      }
      if (plan.takeProfit >= plan.entryPrice) {
        newErrors.takeProfit = 'Take profit must be below entry price for short positions';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [plan]);

  const calculateRisk = useCallback((): number => {
    const riskPerShare = Math.abs(plan.entryPrice - plan.stopLoss);
    return riskPerShare * plan.quantity;
  }, [plan.entryPrice, plan.stopLoss, plan.quantity]);

  const updatePlan = useCallback((updates: Partial<TradingPlanData>) => {
    setPlan(prev => ({ ...prev, ...updates }));
  }, []);

  const resetPlan = useCallback(() => {
    setPlan({
      symbol: '',
      direction: 'long',
      entryPrice: 0,
      stopLoss: 0,
      takeProfit: 0,
      quantity: 0,
      reasoning: '',
      keyLevels: [],
      riskReward: 0
    });
    setErrors({});
  }, []);

  // Calculate risk/reward ratio
  useEffect(() => {
    if (plan.entryPrice > 0 && plan.stopLoss > 0 && plan.takeProfit > 0) {
      const risk = Math.abs(plan.entryPrice - plan.stopLoss);
      const reward = Math.abs(plan.takeProfit - plan.entryPrice);
      const ratio = risk > 0 ? reward / risk : 0;

      setPlan(prev => ({ ...prev, riskReward: ratio }));
    }
  }, [plan.entryPrice, plan.stopLoss, plan.takeProfit]);

  const isValid = Object.keys(errors).length === 0 && plan.symbol && plan.entryPrice > 0;

  return {
    plan,
    isValid,
    errors,
    actions: {
      updatePlan,
      validatePlan,
      resetPlan,
      calculateRisk
    }
  };
};