import React from 'react';

interface ProfitLossCellCoreProps {
  value: number;
  percentage: number;
  isPositive: boolean;
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  className?: string;
}

/**
 * Core display component for profit/loss values
 * Extracted from ProfitLossCell for better reusability
 */
export const ProfitLossCellCore: React.FC<ProfitLossCellCoreProps> = ({
  value,
  percentage,
  isPositive,
  size = 'md',
  showPercentage = true,
  className = ''
}) => {
  const baseClasses = `
    flex items-center justify-end font-semibold
    ${isPositive ? 'text-green-600' : 'text-red-600'}
    ${size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'}
    ${className}
  `;

  return (
    <div className={baseClasses}>
      <span className="mr-1">
        {isPositive ? '+' : ''}${value.toFixed(2)}
      </span>
      {showPercentage && (
        <span className="text-xs opacity-75">
          ({isPositive ? '+' : ''}{percentage.toFixed(1)}%)
        </span>
      )}
    </div>
  );
};