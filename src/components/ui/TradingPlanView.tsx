import React from 'react';
import { useTradingPlan } from '../../hooks/useTradingPlan';
import { TradingPlanView } from '../ui/TradingPlanView';

interface TradingPlanContainerProps {
  initialPlan?: any;
  onSave?: (plan: any) => void;
  onCancel?: () => void;
  className?: string;
}

/**
 * Container component that manages trading plan business logic
 * Extracted from TradingPlan component for better separation of concerns
 */
export const TradingPlanContainer: React.FC<TradingPlanContainerProps> = ({
  initialPlan,
  onSave,
  onCancel,
  className
}) => {
  const { plan, isValid, errors, actions } = useTradingPlan(initialPlan);

  const handleSave = () => {
    if (actions.validatePlan() && onSave) {
      onSave(plan);
    }
  };

  const handleCancel = () => {
    actions.resetPlan();
    onCancel?.();
  };

  return (
    <TradingPlanView
      plan={plan}
      errors={errors}
      isValid={isValid}
      onUpdatePlan={actions.updatePlan}
      onSave={handleSave}
      onCancel={handleCancel}
      className={className}
    />
  );
};