import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from './routes';
import AppErrorBoundary from './components/AppErrorBoundary';

/**
 * Main App component for the ADHD Trading Dashboard
 * Using BrowserRouter for better URL structure
 */
function App() {
  return (
    <AppErrorBoundary>
      <ThemeProvider initialTheme="f1">
        <BrowserRouter>
          <AppRoutes />
        </BrowserRouter>
      </ThemeProvider>
    </AppErrorBoundary>
  );
}

export default App;
