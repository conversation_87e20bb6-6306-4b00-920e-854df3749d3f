// Import DevTools configuration first
import './devtools-config';

import React from 'react';
import ReactDOM from 'react-dom/client';
import SimpleApp from '../../../src/components/ui/SimpleApp';

// Get the root element
const rootElement = document.getElementById('root');

// Create a fallback root element if needed
if (!rootElement) {
  console.error('Root element not found, creating a fallback element');
  const fallbackRoot = document.createElement('div');
  fallbackRoot.id = 'root';
  document.body.appendChild(fallbackRoot);
}

// Create the React root
const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

// Render the app
root.render(
  <React.StrictMode>
    <SimpleApp />
  </React.StrictMode>
);
