/**
 * Daily Guide Tests
 * 
 * Tests for the Daily Guide feature
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import { DailyGuide } from '../index';
import { fetchDailyGuideData } from '../../../../../../src/services/dailyGuideApi';

// Mock the API
vi.mock('../api/dailyGuideApi', () => ({
  fetchDailyGuideData: vi.fn(),
}));

// Mock the theme
const mockTheme = {
  colors: {
    textPrimary: '#000',
    textSecondary: '#666',
    background: '#fff',
    surface: '#f5f5f5',
    primary: '#ff0000',
    primaryDark: '#cc0000',
    primaryLight: '#ff3333',
    secondary: '#0000ff',
    secondaryDark: '#0000cc',
    secondaryLight: '#3333ff',
    profit: '#00cc00',
    loss: '#cc0000',
    neutral: '#cccccc',
    error: '#ff0000',
    warning: '#ffcc00',
    info: '#0099ff',
    accent: '#ff9900',
  },
  spacing: {
    xxs: '4px',
    xs: '8px',
    sm: '12px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  fontSizes: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '24px',
    xxl: '32px',
    h1: '32px',
    h2: '24px',
    h3: '20px',
    h4: '18px',
    h5: '16px',
    h6: '14px',
  },
  borderRadius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    pill: '999px',
    circle: '50%',
  },
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
  },
  transitions: {
    fast: '0.2s ease',
    normal: '0.3s ease',
    slow: '0.5s ease',
  },
  fontWeights: {
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,
  },
  fontFamilies: {
    body: 'Arial, sans-serif',
    heading: 'Arial, sans-serif',
    mono: 'monospace',
  },
  zIndex: {
    base: 0,
    overlay: 10,
    modal: 20,
    popover: 30,
    tooltip: 40,
    fixed: 50,
  },
  name: 'default',
  breakpoints: {
    xs: '480px',
    sm: '768px',
    md: '992px',
    lg: '1200px',
    xl: '1600px',
  },
};

// Mock data
const mockData = {
  marketData: {
    sentiment: 'bullish' as const,
    summary: 'Markets are showing strong bullish momentum.',
    indices: [
      { name: 'S&P 500', value: 4550.25, change: 1.2, previousClose: 4500.10 },
      { name: 'Nasdaq', value: 14200.50, change: 1.5, previousClose: 14000.25 },
    ],
    lastUpdated: new Date().toISOString(),
  },
  tradingPlan: [
    { id: '1', description: 'Wait for market open', priority: 'high' as const, completed: false },
    { id: '2', description: 'Focus on tech sector', priority: 'medium' as const, completed: false },
  ],
  keyLevels: [
    {
      symbol: 'SPY',
      support: ['450.00', '445.75'],
      resistance: ['455.50', '460.00'],
      pivotPoint: '452.25',
    },
  ],
  marketNews: [
    {
      id: '1',
      title: 'Fed signals potential rate hike',
      source: 'Financial Times',
      url: 'https://example.com/news/1',
      timestamp: new Date().toISOString(),
      impact: 'high' as const,
    },
  ],
};

describe('DailyGuide', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (fetchDailyGuideData as any).mockResolvedValue(mockData);
  });

  it('renders the daily guide with all sections', async () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <DailyGuide />
      </ThemeProvider>
    );

    // Check that the title is rendered
    expect(screen.getByText('Daily Trading Guide')).toBeInTheDocument();

    // Wait for data to load
    await waitFor(() => {
      // Check that all sections are rendered
      expect(screen.getByText('Market Overview')).toBeInTheDocument();
      expect(screen.getByText('Trading Plan')).toBeInTheDocument();
      expect(screen.getByText('Key Levels')).toBeInTheDocument();
      expect(screen.getByText('Market News')).toBeInTheDocument();
    });

    // Check that market data is rendered
    expect(screen.getByText('Markets are showing strong bullish momentum.')).toBeInTheDocument();
    expect(screen.getByText('S&P 500')).toBeInTheDocument();
    expect(screen.getByText('Nasdaq')).toBeInTheDocument();

    // Check that trading plan is rendered
    expect(screen.getByText('Wait for market open')).toBeInTheDocument();
    expect(screen.getByText('Focus on tech sector')).toBeInTheDocument();

    // Check that key levels are rendered
    expect(screen.getByText('SPY')).toBeInTheDocument();

    // Check that market news is rendered
    expect(screen.getByText('Fed signals potential rate hike')).toBeInTheDocument();
  });

  it('handles refresh button click', async () => {
    render(
      <ThemeProvider theme={mockTheme}>
        <DailyGuide />
      </ThemeProvider>
    );

    // Wait for initial data to load
    await waitFor(() => {
      expect(screen.getByText('Market Overview')).toBeInTheDocument();
    });

    // Click the refresh button
    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);

    // Check that the API was called again
    await waitFor(() => {
      expect(fetchDailyGuideData).toHaveBeenCalledTimes(2);
    });
  });
});
