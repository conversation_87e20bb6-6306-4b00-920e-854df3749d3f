/**
 * Daily Guide Context
 *
 * Context for managing daily guide state
 */

import React, {
  createContext,
  useContext,
  useReducer,
  ReactNode,
  useCallback,
  useEffect,
} from 'react';
import {
  DailyGuideState,
  DailyGuideAction,
  // Using these types but not directly referencing them in variable declarations
  // DailyGuideData,
  // MarketData,
  // TradingPlanItem,
  // KeyLevel,
  // MarketNews
} from '../types';
import { fetchDailyGuideData } from '../../../../../../src/services/dailyGuideApi';

// Initial state
const initialState: DailyGuideState = {
  marketData: null,
  tradingPlan: [],
  keyLevels: [],
  marketNews: [],
  isLoading: false,
  error: null,
  currentDate: new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }),
};

// Reducer function
const dailyGuideReducer = (state: DailyGuideState, action: DailyGuideAction): DailyGuideState => {
  switch (action.type) {
    case 'FETCH_DATA_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'FETCH_DATA_SUCCESS':
      return {
        ...state,
        ...action.payload,
        isLoading: false,
        error: null,
      };
    case 'FETCH_DATA_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case 'UPDATE_TRADING_PLAN_ITEM':
      return {
        ...state,
        tradingPlan: state.tradingPlan.map((item) =>
          item.id === action.payload.id ? { ...item, completed: action.payload.completed } : item
        ),
      };
    case 'REFRESH_DATA':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    default:
      return state;
  }
};

// Context
interface DailyGuideContextType extends DailyGuideState {
  fetchGuideData: () => Promise<void>;
  updateTradingPlanItem: (id: string, completed: boolean) => void;
  refreshData: () => void;
}

const DailyGuideContext = createContext<DailyGuideContextType | undefined>(undefined);

// Provider component
interface DailyGuideProviderProps {
  children: ReactNode;
}

export const DailyGuideProvider: React.FC<DailyGuideProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(dailyGuideReducer, initialState);

  const fetchGuideData = useCallback(async () => {
    dispatch({ type: 'FETCH_DATA_START' });
    try {
      const data = await fetchDailyGuideData();
      dispatch({ type: 'FETCH_DATA_SUCCESS', payload: data });
    } catch (error) {
      dispatch({
        type: 'FETCH_DATA_ERROR',
        payload: error instanceof Error ? error.message : 'An unknown error occurred',
      });
    }
  }, []);

  const updateTradingPlanItem = useCallback((id: string, completed: boolean) => {
    dispatch({ type: 'UPDATE_TRADING_PLAN_ITEM', payload: { id, completed } });
  }, []);

  const refreshData = useCallback(() => {
    dispatch({ type: 'REFRESH_DATA' });
    fetchGuideData();
  }, [fetchGuideData]);

  // Load data on mount
  useEffect(() => {
    fetchGuideData();
  }, [fetchGuideData]);

  const value = {
    ...state,
    fetchGuideData,
    updateTradingPlanItem,
    refreshData,
  };

  return <DailyGuideContext.Provider value={value}>{children}</DailyGuideContext.Provider>;
};

// Custom hook for using the context
export const useDailyGuide = (): DailyGuideContextType => {
  const context = useContext(DailyGuideContext);
  if (context === undefined) {
    throw new Error('useDailyGuide must be used within a DailyGuideProvider');
  }
  return context;
};
