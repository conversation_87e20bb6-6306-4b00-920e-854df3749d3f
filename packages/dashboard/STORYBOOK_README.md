# ADHD Trading Dashboard - Storybook Example

## 🎯 What is Storybook?

Storybook is a visual gallery of your UI components that helps you:
- **See all component states** without clicking through your app
- **Test components in isolation** with different props and data
- **Document components** automatically with interactive examples
- **Catch visual bugs** before they reach production

## 🧠 Perfect for ADHD Trading

Storybook is especially valuable for ADHD traders because it provides:
- **Visual consistency** - See all P&L states at a glance
- **Quick testing** - No need to create fake trades to test UI
- **State documentation** - Remember how components should look
- **Instant feedback** - See changes immediately

## 🚀 Getting Started

### 1. Install Dependencies
```bash
cd packages/dashboard
yarn install
```

### 2. Run Storybook
```bash
yarn storybook
```

This will open Storybook at `http://localhost:6006`

## 📊 ProfitLossCell Example

We've created a complete example with the `ProfitLossCell` component that shows:

### Component Features
- ✅ **Color-coded P&L** (Green profits, Red losses, Gray breakeven)
- ✅ **Multiple sizes** (Small for tables, Medium for cards, Large for emphasis)
- ✅ **Loading states** with shimmer animation
- ✅ **Accessibility** with proper ARIA labels
- ✅ **Currency formatting** with locale support

### Stories Included
1. **Basic Examples**: Big Win, Small Win, Breakeven, Small Loss, Big Loss
2. **Interactive Playground**: Adjust props in real-time
3. **Size Variants**: See all sizes side-by-side
4. **Trading Scenarios**: Real-world trading amounts
5. **Table Context**: How it looks in actual trading tables
6. **Edge Cases**: Large numbers, null values, different currencies

## 🎨 Visual Benefits

### Before Storybook
- Manual testing by creating fake trades
- Hard to see all P&L states together
- Difficult to test edge cases
- No visual documentation

### After Storybook
- All states visible in one place
- Interactive testing with controls
- Comprehensive edge case coverage
- Living documentation that stays updated

## 🔧 File Structure

```
packages/dashboard/
├── .storybook/
│   ├── main.ts          # Storybook configuration
│   └── preview.tsx      # Global decorators & theme
├── src/components/molecules/
│   ├── ProfitLossCell.tsx         # Component
│   └── ProfitLossCell.stories.tsx # Stories
```

## 📈 Next Steps

Apply this pattern to your other trading components:

1. **TradeTable** - Show different data scenarios
2. **MetricsPanel** - Display various performance states
3. **TradeForm** - Test validation and calculation states
4. **PerformanceChart** - Visualize different chart data

## 🎯 ADHD Trading Benefits

- **Reduced cognitive load** - See everything at once
- **Faster development** - No context switching
- **Better testing** - Cover all scenarios easily
- **Visual memory aids** - Remember component behaviors
- **Consistent UI** - Catch inconsistencies quickly

## 🚨 Troubleshooting

If you encounter dependency issues:
1. Delete `node_modules` and `yarn.lock`
2. Run `yarn install` from the root directory
3. Try `yarn storybook` again

## 📚 Learn More

- [Storybook Documentation](https://storybook.js.org/docs)
- [React Storybook Tutorial](https://storybook.js.org/tutorials/intro-to-storybook/react/en/get-started/)
- [Accessibility Testing](https://storybook.js.org/addons/@storybook/addon-a11y)
