{"name": "adhd-trading-dashboard-lib", "version": "1.0.0", "description": "ADHD Trading Dashboard React Library", "private": true, "workspaces": ["packages/*"], "scripts": {"start": "yarn workspace @adhd-trading-dashboard/dashboard start", "dev": "yarn workspace @adhd-trading-dashboard/dashboard dev", "build": "node scripts/build.js", "build:clean": "node scripts/build.js --clean", "build:dev": "node scripts/build.js --dev", "build:validate": "node scripts/build.js --validate", "build:shared": "node scripts/build.js shared", "build:dashboard": "node scripts/build.js dashboard", "analyze": "webpack --mode production --env analyze=true", "deploy": "node scripts/deploy-monorepo.js", "deploy:all": "node scripts/deploy-monorepo.js --deploy", "deploy:gh-pages": "yarn build && node scripts/deploy-gh-pages.js", "type-check": "tsc --build", "type-check:watch": "tsc --build --watch", "lint": "eslint packages --ext .js,.jsx,.ts,.tsx", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "storybook": "yarn workspace @adhd-trading-dashboard/shared storybook", "build-storybook": "yarn workspace @adhd-trading-dashboard/shared build-storybook", "clean": "node scripts/clean.js", "clean:deep": "node scripts/clean.js --deep", "check-versions": "node scripts/manage-versions.js --check", "fix-versions": "node scripts/manage-versions.js", "manage-assets": "node scripts/manage-assets.js", "diagnostics": "node scripts/diagnostics/run-all.js", "postinstall": "yarn check-versions", "health": "node code-health/orchestrate-health.js", "health:analyze": "node code-health/enhanced-analyze-codebase.js", "health:scripts": "node code-health/enhanced-refactor-analyzer.js ./scripts", "health:cleanup": "node code-health/dynamic-scripts-cleanup.js", "health:cleanup:dry": "node code-health/dynamic-scripts-cleanup.js --dry-run", "health:setup": "node setup-code-health.js", "// Note": "All health scripts should be run with yarn (e.g., 'yarn health:setup') for proper monorepo compatibility", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"core-js": "3.35.0", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.6.2", "recharts": "2.10.3", "styled-components": "5.3.6"}, "devDependencies": {"@babel/cli": "7.23.9", "@babel/core": "7.23.6", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "7.23.6", "@babel/preset-env": "7.23.6", "@babel/preset-react": "7.23.3", "@babel/preset-typescript": "7.23.3", "@babel/runtime": "7.23.6", "@playwright/test": "1.40.1", "@storybook/addon-a11y": "7.6.7", "@storybook/addon-docs": "7.6.7", "@storybook/addon-essentials": "7.6.7", "@storybook/addon-interactions": "7.6.7", "@storybook/addon-links": "7.6.7", "@storybook/react": "7.6.7", "@storybook/react-webpack5": "7.6.7", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "@types/node": "16.18.11", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "@types/react-router-dom": "5.3.3", "@types/styled-components": "5.1.26", "@typescript-eslint/eslint-plugin": "5.59.0", "@typescript-eslint/parser": "5.59.0", "@vitejs/plugin-react": "4.0.0", "acorn": "8.14.1", "babel-loader": "9.1.3", "babel-plugin-styled-components": "2.1.4", "chalk": "4.1.2", "css-loader": "6.8.1", "css-minimizer-webpack-plugin": "5.0.1", "eslint": "8.56.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "glob": "8.1.0", "html-webpack-plugin": "5.6.3", "mini-css-extract-plugin": "2.9.2", "rimraf": "5.0.0", "schema-utils": "3.3.0", "storybook": "7.6.7", "style-loader": "3.3.3", "terser-webpack-plugin": "5.3.14", "typescript": "4.9.4", "vite": "4.3.1", "vitest": "0.30.1", "webpack": "5.89.0", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.1", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "ts-jest": "^29.0.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"typescript": "4.9.4", "@types/react": "18.0.28", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "5.3.6", "@typescript-eslint/typescript-estree": "5.59.0", "react-scripts": "5.0.1", "@typescript-eslint/parser": "5.59.0"}, "keywords": ["react", "trading", "dashboard", "typescript"], "author": "", "license": "MIT", "type": "module"}