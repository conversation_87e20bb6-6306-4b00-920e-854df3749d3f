{"currentPhase": 3, "completedPhases": [1, 2], "failedPhases": [{"phase": 3, "error": "Command failed: node ./migration-scripts/phase3-hooks.cjs ", "timestamp": "2025-05-25T08:09:17.410Z"}], "backups": [{"phase": 1, "backup": "/Users/<USER>/adhd-trading-dashboard-lib/migration-backups/phase-Create-New-Folder-Structure-1748160443100", "timestamp": "2025-05-25T08:07:23.524Z"}, {"phase": 2, "backup": "/Users/<USER>/adhd-trading-dashboard-lib/migration-backups/phase-Move-Utility-Functions-1748160482492", "timestamp": "2025-05-25T08:08:02.758Z"}, {"phase": 3, "backup": "/Users/<USER>/adhd-trading-dashboard-lib/migration-backups/phase-Extract-and-Move-Custom-Hooks-1748160556798", "timestamp": "2025-05-25T08:09:17.110Z"}]}