#!/usr/bin/env node

/**
 * Phase 5: Reorganize Components
 * Move components to their appropriate folders based on architectural patterns
 * Estimated time: 1-2 days
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class Phase5ComponentReorganizer {
  constructor(dryRun = false) {
    this.dryRun = dryRun;
    this.componentMoves = [
      // UI Components - Basic, reusable components
      {
        name: 'App',
        from: 'packages/dashboard/src/App.tsx',
        to: 'src/components/ui/App.tsx',
        category: 'ui',
        reason: 'Basic app shell component'
      },
      {
        name: 'MinimalApp',
        from: 'packages/dashboard/src/MinimalApp.tsx',
        to: 'src/components/ui/MinimalApp.tsx',
        category: 'ui',
        reason: 'Minimal app variant'
      },
      {
        name: 'SimpleApp',
        from: 'packages/dashboard/src/SimpleApp.tsx',
        to: 'src/components/ui/SimpleApp.tsx',
        category: 'ui',
        reason: 'Simple app variant'
      },

      // Feature Components - Business logic components
      {
        name: 'TestApp',
        from: 'packages/dashboard/src/TestApp.tsx',
        to: 'src/components/feature/TestApp.tsx',
        category: 'feature',
        reason: 'Test-specific functionality'
      },
      {
        name: 'TradingDashboard',
        from: 'packages/dashboard/src/features/trading-dashboard/TradingDashboard.tsx',
        to: 'src/components/feature/TradingDashboard.tsx',
        category: 'feature',
        reason: 'Core trading dashboard feature'
      },
      {
        name: 'TradeListRow',
        from: 'packages/dashboard/src/features/trading-dashboard/components/TradeListRow.tsx',
        to: 'src/components/feature/TradeListRow.tsx',
        category: 'feature',
        reason: 'Trade-specific list component'
      },
      {
        name: 'KeyLevels',
        from: 'packages/dashboard/src/features/daily-guide/components/KeyLevels.tsx',
        to: 'src/components/feature/KeyLevels.tsx',
        category: 'feature',
        reason: 'Trading key levels feature'
      },

      // Layout Components - Headers, navigation, structure
      {
        name: 'TradeFormHeader',
        from: 'packages/dashboard/src/features/trading-dashboard/components/TradeFormHeader.tsx',
        to: 'src/components/layout/TradeFormHeader.tsx',
        category: 'layout',
        reason: 'Header layout component'
      },
      {
        name: 'TradeJournalHeader',
        from: 'packages/dashboard/src/features/trading-dashboard/components/TradeJournalHeader.tsx',
        to: 'src/components/layout/TradeJournalHeader.tsx',
        category: 'layout',
        reason: 'Journal header layout'
      },
      {
        name: 'TradeListHeader',
        from: 'packages/dashboard/src/features/trading-dashboard/components/TradeListHeader.tsx',
        to: 'src/components/layout/TradeListHeader.tsx',
        category: 'layout',
        reason: 'List header layout'
      },

      // Pages - Top-level page components
      {
        name: 'DailyGuide',
        from: 'packages/dashboard/src/features/daily-guide/DailyGuide.tsx',
        to: 'src/pages/DailyGuide.tsx',
        category: 'pages',
        reason: 'Daily guide page component'
      },
      {
        name: 'Settings',
        from: 'packages/dashboard/src/features/settings/Settings.tsx',
        to: 'src/pages/Settings.tsx',
        category: 'pages',
        reason: 'Settings page component'
      },

      // Services - API and external integrations
      {
        name: 'dailyGuideApi',
        from: 'packages/dashboard/src/features/daily-guide/api/dailyGuideApi.ts',
        to: 'src/services/dailyGuideApi.ts',
        category: 'services',
        reason: 'API service for daily guide'
      },
      {
        name: 'tradeAnalysisApi',
        from: 'packages/dashboard/src/features/trading-dashboard/api/tradeAnalysisApi.ts',
        to: 'src/services/tradeAnalysisApi.ts',
        category: 'services',
        reason: 'API service for trade analysis'
      },

      // Types - Shared type definitions
      {
        name: 'types',
        from: 'packages/dashboard/src/types/types.ts',
        to: 'src/types/trade-types.ts',
        category: 'types',
        reason: 'Trading-related type definitions'
      },
      {
        name: 'react-types',
        from: 'packages/dashboard/src/react-types.d.ts',
        to: 'src/types/react-types.d.ts',
        category: 'types',
        reason: 'React component type definitions'
      }
    ];

    this.movedFiles = [];
    this.importMappings = new Map();
  }

  log(message) {
    console.log(`[Phase 5] ${message}`);
  }

  async analyzeComponentDependencies() {
    this.log('🔍 Analyzing component dependencies...');
    
    const dependencies = new Map();
    
    for (const move of this.componentMoves) {
      const fromPath = path.join(process.cwd(), move.from);
      
      if (!fs.existsSync(fromPath)) {
        this.log(`⚠️  Source file does not exist: ${move.from}`);
        continue;
      }

      try {
        const content = fs.readFileSync(fromPath, 'utf8');
        const imports = this.extractImports(content);
        dependencies.set(move.name, imports);
      } catch (error) {
        this.log(`❌ Failed to analyze dependencies for ${move.name}: ${error.message}`);
      }
    }

    return dependencies;
  }

  extractImports(content) {
    const imports = [];
    const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push({
        statement: match[0],
        path: match[1]
      });
    }
    
    return imports;
  }

  moveComponent(moveConfig) {
    const fromPath = path.join(process.cwd(), moveConfig.from);
    const toPath = path.join(process.cwd(), moveConfig.to);

    if (!fs.existsSync(fromPath)) {
      this.log(`⚠️  Source component does not exist: ${moveConfig.from}`);
      return;
    }

    if (fs.existsSync(toPath)) {
      this.log(`⚠️  Destination component already exists: ${moveConfig.to}`);
      return;
    }

    if (this.dryRun) {
      this.log(`📦 [DRY RUN] Would move: ${moveConfig.name} → ${moveConfig.category}/${path.basename(moveConfig.to)}`);
      return;
    }

    try {
      // Ensure destination directory exists
      const toDir = path.dirname(toPath);
      fs.mkdirSync(toDir, { recursive: true });

      // Use git mv for proper tracking
      execSync(`git mv "${fromPath}" "${toPath}"`);
      this.log(`✅ Moved: ${moveConfig.name} → ${moveConfig.category}/${path.basename(moveConfig.to)}`);

      // Store mapping for import updates
      this.importMappings.set(moveConfig.from, moveConfig.to);
      this.movedFiles.push(moveConfig);
    } catch (error) {
      this.log(`❌ Failed to move ${moveConfig.name}: ${error.message}`);
      throw error;
    }
  }

  updateImportsInFiles() {
    this.log('🔄 Updating import statements across codebase...');

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would update imports in all affected files');
      return;
    }

    // Find all source files that might need import updates
    const sourceFiles = this.findAllSourceFiles();
    
    let updatedFiles = 0;
    for (const filePath of sourceFiles) {
      if (this.updateImportsInFile(filePath)) {
        updatedFiles++;
      }
    }

    this.log(`✅ Updated imports in ${updatedFiles} files`);
  }

  findAllSourceFiles() {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const ignorePaths = ['node_modules', '.git', 'dist', 'build', 'coverage'];
    
    const findFiles = (dir) => {
      const files = [];
      
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !ignorePaths.includes(item)) {
            files.push(...findFiles(fullPath));
          } else if (stat.isFile() && extensions.includes(path.extname(item))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
      
      return files;
    };
    
    return findFiles(process.cwd());
  }

  updateImportsInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let updatedContent = content;
      let hasChanges = false;

      // Update imports for moved files
      for (const [oldPath, newPath] of this.importMappings) {
        // Calculate relative paths
        const oldRelativePath = this.getRelativeImportPath(filePath, oldPath);
        const newRelativePath = this.getRelativeImportPath(filePath, newPath);

        // Skip if paths are the same
        if (oldRelativePath === newRelativePath) continue;

        // Update various import patterns
        const patterns = [
          // Standard imports: import X from 'path'
          new RegExp(`(import\\s+[^'"\`]*from\\s+['"\`])${this.escapeRegex(oldRelativePath)}(['"\`])`, 'g'),
          // Require statements: require('path')
          new RegExp(`(require\\(['"\`])${this.escapeRegex(oldRelativePath)}(['"\`]\\))`, 'g'),
          // Dynamic imports: import('path')
          new RegExp(`(import\\(['"\`])${this.escapeRegex(oldRelativePath)}(['"\`]\\))`, 'g')
        ];

        for (const pattern of patterns) {
          const newContent = updatedContent.replace(pattern, `$1${newRelativePath}$2`);
          if (newContent !== updatedContent) {
            updatedContent = newContent;
            hasChanges = true;
          }
        }
      }

      if (hasChanges) {
        fs.writeFileSync(filePath, updatedContent);
        return true;
      }

      return false;
    } catch (error) {
      this.log(`⚠️  Failed to update imports in ${filePath}: ${error.message}`);
      return false;
    }
  }

  getRelativeImportPath(fromFile, toFile) {
    const fromDir = path.dirname(fromFile);
    const relativePath = path.relative(fromDir, toFile);
    
    // Remove file extension for imports
    const withoutExt = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Ensure relative paths start with './' or '../'
    if (!withoutExt.startsWith('.')) {
      return './' + withoutExt;
    }
    
    return withoutExt;
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  createCategoryIndexFiles() {
    this.log('📄 Creating category index files...');

    const categories = {
      ui: 'Basic reusable UI components',
      feature: 'Business feature components',
      layout: 'Layout and structural components',
      pages: 'Top-level page components'
    };

    for (const [category, description] of Object.entries(categories)) {
      this.createCategoryIndex(category, description);
    }
  }

  createCategoryIndex(category, description) {
    const categoryFiles = this.movedFiles.filter(move => move.category === category);
    const indexPath = path.join(process.cwd(), `src/components/${category}/index.ts`);

    if (this.dryRun) {
      this.log(`📝 [DRY RUN] Would create ${category} index with ${categoryFiles.length} exports`);
      return;
    }

    const exports = categoryFiles.map(move => {
      const fileName = path.basename(move.to, path.extname(move.to));
      return `export { ${move.name} } from './${fileName}';`;
    }).join('\n');

    const content = `/**
 * ${description}
 */

${exports}
`;

    try {
      fs.writeFileSync(indexPath, content);
      this.log(`✅ Created ${category} index with ${categoryFiles.length} exports`);
    } catch (error) {
      this.log(`❌ Failed to create ${category} index: ${error.message}`);
      throw error;
    }
  }

  updateMainIndexFiles() {
    this.log('📄 Updating main index files...');

    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would update main component and service indexes');
      return;
    }

    // Update main components index
    this.updateMainComponentsIndex();

    // Update services index
    this.updateServicesIndex();

    // Update types index
    this.updateTypesIndex();

    // Update pages index if it doesn't exist
    this.createPagesIndex();
  }

  updateMainComponentsIndex() {
    const indexPath = path.join(process.cwd(), 'src/components/index.ts');
    const content = `/**
 * Component exports organized by category
 * Auto-generated during architectural reorganization
 */

// UI Components - Basic, reusable components
export * from './ui';

// Feature Components - Business logic components
export * from './feature';

// Layout Components - App structure and navigation
export * from './layout';
`;

    try {
      fs.writeFileSync(indexPath, content);
      this.log('✅ Updated main components index');
    } catch (error) {
      this.log(`❌ Failed to update main components index: ${error.message}`);
    }
  }

  updateServicesIndex() {
    const serviceFiles = this.movedFiles.filter(move => move.category === 'services');
    if (serviceFiles.length === 0) return;

    const indexPath = path.join(process.cwd(), 'src/services/index.ts');
    const exports = serviceFiles.map(move => {
      const fileName = path.basename(move.to, path.extname(move.to));
      return `export * from './${fileName}';`;
    }).join('\n');

    const content = `/**
 * Service layer exports
 * API clients and external service integrations
 */

${exports}
`;

    try {
      fs.writeFileSync(indexPath, content);
      this.log('✅ Updated services index');
    } catch (error) {
      this.log(`❌ Failed to update services index: ${error.message}`);
    }
  }

  updateTypesIndex() {
    const typeFiles = this.movedFiles.filter(move => move.category === 'types');
    if (typeFiles.length === 0) return;

    const indexPath = path.join(process.cwd(), 'src/types/index.ts');
    const exports = typeFiles.map(move => {
      const fileName = path.basename(move.to, path.extname(move.to));
      return `export * from './${fileName}';`;
    }).join('\n');

    const content = `/**
 * TypeScript type definitions exports
 * Shared types used across the application
 */

${exports}
`;

    try {
      fs.writeFileSync(indexPath, content);
      this.log('✅ Updated types index');
    } catch (error) {
      this.log(`❌ Failed to update types index: ${error.message}`);
    }
  }

  createPagesIndex() {
    const pageFiles = this.movedFiles.filter(move => move.category === 'pages');
    if (pageFiles.length === 0) return;

    const indexPath = path.join(process.cwd(), 'src/pages/index.ts');
    const exports = pageFiles.map(move => {
      const fileName = path.basename(move.to, path.extname(move.to));
      return `export { ${move.name} } from './${fileName}';`;
    }).join('\n');

    const content = `/**
 * Page component exports
 * Top-level page components for routing
 */

${exports}
`;

    try {
      fs.writeFileSync(indexPath, content);
      this.log('✅ Created pages index');
    } catch (error) {
      this.log(`❌ Failed to create pages index: ${error.message}`);
    }
  }

  generateMigrationReport() {
    if (this.dryRun) {
      this.log('📊 [DRY RUN] Would generate migration report');
      return;
    }

    const reportPath = path.join(process.cwd(), 'phase5-migration-report.md');
    const reportContent = this.createReportContent();

    try {
      fs.writeFileSync(reportPath, reportContent);
      this.log('✅ Generated migration report');
    } catch (error) {
      this.log(`❌ Failed to generate migration report: ${error.message}`);
    }
  }

  createReportContent() {
    const categoryCounts = this.movedFiles.reduce((acc, move) => {
      acc[move.category] = (acc[move.category] || 0) + 1;
      return acc;
    }, {});

    return `# Phase 5: Component Reorganization Report

Generated: ${new Date().toISOString()}

## Summary

Total files moved: ${this.movedFiles.length}
Import mappings created: ${this.importMappings.size}

## Files by Category

${Object.entries(categoryCounts).map(([category, count]) => 
  `- **${category}**: ${count} files`
).join('\n')}

## Detailed File Moves

${this.movedFiles.map(move => `
### ${move.name}
- **From**: \`${move.from}\`
- **To**: \`${move.to}\`
- **Category**: ${move.category}
- **Reason**: ${move.reason}
`).join('\n')}

## Import Mappings

${Array.from(this.importMappings.entries()).map(([from, to]) => 
  `- \`${from}\` → \`${to}\``
).join('\n')}

## Next Steps

1. Verify all imports are working correctly
2. Run tests to ensure no functionality is broken
3. Update any remaining hardcoded paths
4. Consider running Phase 6 to add missing tests
5. Update documentation to reflect new structure
`;
  }

  validateReorganization() {
    this.log('🔍 Validating component reorganization...');
    
    let errors = 0;
    
    for (const move of this.movedFiles) {
      const toPath = path.join(process.cwd(), move.to);
      
      if (!fs.existsSync(toPath)) {
        this.log(`❌ Moved file does not exist: ${move.to}`);
        errors++;
      } else {
        this.log(`✅ File exists at new location: ${move.name}`);
      }
    }

    // Check that old locations are empty or removed
    for (const move of this.movedFiles) {
      const fromPath = path.join(process.cwd(), move.from);
      
      if (fs.existsSync(fromPath)) {
        this.log(`⚠️  Original file still exists: ${move.from}`);
        // This might be intentional in some cases
      }
    }

    if (errors > 0) {
      throw new Error(`Validation failed with ${errors} errors`);
    }

    this.log('✅ Component reorganization validation passed');
  }

  async run() {
    this.log(`🚀 Starting Phase 5: Reorganize Components ${this.dryRun ? '(DRY RUN)' : ''}`);
    
    try {
      // Analyze dependencies before moving
      this.log('🔍 Analyzing component dependencies...');
      await this.analyzeComponentDependencies();

      // Move components to new locations
      this.log('📦 Moving components to new locations...');
      for (const moveConfig of this.componentMoves) {
        this.moveComponent(moveConfig);
      }

      // Update import statements across the codebase
      this.log('🔄 Updating import statements...');
      this.updateImportsInFiles();

      // Create category index files
      this.log('📄 Creating category index files...');
      this.createCategoryIndexFiles();

      // Update main index files
      this.log('📄 Updating main index files...');
      this.updateMainIndexFiles();

      // Generate migration report
      this.log('📊 Generating migration report...');
      this.generateMigrationReport();

      // Validate reorganization
      if (!this.dryRun) {
        this.validateReorganization();
      }

      this.log(`✅ Phase 5 completed successfully! ${this.dryRun ? '(DRY RUN)' : ''}`);
      
      if (!this.dryRun) {
        this.log('📋 Next steps:');
        this.log('  1. Test that all imports are working correctly');
        this.log('  2. Run the application to verify functionality');
        this.log('  3. Run Phase 6 to add missing tests');
        this.log(`  4. Moved ${this.movedFiles.length} files to new locations`);
        this.log('  5. Review phase5-migration-report.md for details');
      }

    } catch (error) {
      this.log(`❌ Phase 5 failed: ${error.message}`);
      throw error;
    }
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  
  const phase5 = new Phase5ComponentReorganizer(dryRun);
  phase5.run().catch(error => {
    console.error('❌ Phase 5 failed:', error.message);
    process.exit(1);
  });
}

module.exports = Phase5ComponentReorganizer;