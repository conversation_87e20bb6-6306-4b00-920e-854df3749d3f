#!/usr/bin/env node

/**
 * Validation and Rollback Scripts
 * Comprehensive validation tools and rollback capabilities for migration phases
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ValidationRollbackManager {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.verbose = options.verbose || false;
    this.validationResults = {
      structure: { passed: 0, failed: 0, errors: [] },
      imports: { passed: 0, failed: 0, errors: [] },
      syntax: { passed: 0, failed: 0, errors: [] },
      tests: { passed: 0, failed: 0, errors: [] },
      build: { passed: 0, failed: 0, errors: [] }
    };
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[Validation${this.dryRun ? ' - DRY RUN' : ''}]`;
    
    if (level === 'verbose' && !this.verbose) return;
    
    console.log(`${prefix} ${message}`);
  }

  /**
   * Validate folder structure matches architectural plan
   */
  validateFolderStructure() {
    this.log('🔍 Validating folder structure...');
    
    const expectedFolders = [
      'src/components/ui',
      'src/components/feature',
      'src/components/layout',
      'src/pages',
      'src/hooks',
      'src/services',
      'src/utils',
      'src/types'
    ];

    const requiredFiles = [
      'src/components/index.ts',
      'src/hooks/index.ts',
      'src/services/index.ts',
      'src/utils/index.ts',
      'src/types/index.ts'
    ];

    for (const folder of expectedFolders) {
      const fullPath = path.join(process.cwd(), folder);
      
      if (fs.existsSync(fullPath)) {
        this.validationResults.structure.passed++;
        this.log(`✅ Folder exists: ${folder}`, 'verbose');
      } else {
        this.validationResults.structure.failed++;
        this.validationResults.structure.errors.push(`Missing folder: ${folder}`);
        this.log(`❌ Missing folder: ${folder}`);
      }
    }

    for (const file of requiredFiles) {
      const fullPath = path.join(process.cwd(), file);
      
      if (fs.existsSync(fullPath)) {
        this.validationResults.structure.passed++;
        this.log(`✅ Index file exists: ${file}`, 'verbose');
      } else {
        this.validationResults.structure.failed++;
        this.validationResults.structure.errors.push(`Missing index file: ${file}`);
        this.log(`❌ Missing index file: ${file}`);
      }
    }

    this.log(`Structure validation: ${this.validationResults.structure.passed} passed, ${this.validationResults.structure.failed} failed`);
  }

  /**
   * Validate all import statements are resolvable
   */
  async validateImports() {
    this.log('🔍 Validating import statements...');
    
    const sourceFiles = this.findSourceFiles();
    
    for (const filePath of sourceFiles) {
      await this.validateImportsInFile(filePath);
    }

    this.log(`Import validation: ${this.validationResults.imports.passed} passed, ${this.validationResults.imports.failed} failed`);
  }

  async validateImportsInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const imports = this.extractImports(content);
      
      for (const importInfo of imports) {
        const resolvedPath = this.resolveImportPath(importInfo.path, filePath);
        
        if (resolvedPath && fs.existsSync(resolvedPath)) {
          this.validationResults.imports.passed++;
          this.log(`✅ Import resolved: ${importInfo.path} in ${path.relative(process.cwd(), filePath)}`, 'verbose');
        } else {
          this.validationResults.imports.failed++;
          const error = `Unresolved import: ${importInfo.path} in ${path.relative(process.cwd(), filePath)}`;
          this.validationResults.imports.errors.push(error);
          this.log(`❌ ${error}`);
        }
      }
    } catch (error) {
      this.validationResults.imports.failed++;
      const errorMsg = `Failed to validate imports in ${filePath}: ${error.message}`;
      this.validationResults.imports.errors.push(errorMsg);
      this.log(`❌ ${errorMsg}`);
    }
  }

  resolveImportPath(importPath, fromFile) {
    // Skip external packages
    if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
      return null;
    }

    const fromDir = path.dirname(fromFile);
    const resolvedPath = path.resolve(fromDir, importPath);
    
    // Try different extensions
    const extensions = ['.ts', '.tsx', '.js', '.jsx', '.json'];
    
    // Check if it's a file
    for (const ext of extensions) {
      const withExt = resolvedPath + ext;
      if (fs.existsSync(withExt)) {
        return withExt;
      }
    }

    // Check if it's a directory with index file
    if (fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isDirectory()) {
      for (const ext of extensions) {
        const indexPath = path.join(resolvedPath, `index${ext}`);
        if (fs.existsSync(indexPath)) {
          return indexPath;
        }
      }
    }

    return null;
  }

  /**
   * Validate syntax of TypeScript/JavaScript files
   */
  async validateSyntax() {
    this.log('🔍 Validating syntax...');
    
    try {
      // Check if TypeScript compiler is available
      execSync('npx tsc --version', { stdio: 'pipe' });
      
      // Run TypeScript compilation check
      const result = execSync('npx tsc --noEmit --skipLibCheck', { 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      
      this.validationResults.syntax.passed++;
      this.log('✅ TypeScript syntax validation passed');
    } catch (error) {
      this.validationResults.syntax.failed++;
      const errorMsg = `TypeScript syntax errors: ${error.stdout || error.message}`;
      this.validationResults.syntax.errors.push(errorMsg);
      this.log(`❌ ${errorMsg}`);
    }

    // Validate individual files with basic syntax checks
    const sourceFiles = this.findSourceFiles();
    
    for (const filePath of sourceFiles) {
      this.validateFileSyntax(filePath);
    }

    this.log(`Syntax validation: ${this.validationResults.syntax.passed} passed, ${this.validationResults.syntax.failed} failed`);
  }

  validateFileSyntax(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Basic syntax checks
      const issues = [];
      
      // Check for common syntax issues
      const lines = content.split('\n');
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Check for mismatched brackets
        const openBrackets = (line.match(/[{[(]/g) || []).length;
        const closeBrackets = (line.match(/[}\])]/g) || []).length;
        
        // Check for unused imports (basic check)
        if (line.includes('import') && line.includes('from')) {
          const importMatch = line.match(/import\s+(?:{([^}]*)}|\*\s+as\s+(\w+)|(\w+))/);
          if (importMatch) {
            const importedItems = importMatch[1] || importMatch[2] || importMatch[3];
            // This is a simplified check - in a real scenario, you'd need more sophisticated analysis
          }
        }
      }
      
      this.validationResults.syntax.passed++;
      this.log(`✅ Syntax OK: ${path.relative(process.cwd(), filePath)}`, 'verbose');
    } catch (error) {
      this.validationResults.syntax.failed++;
      const errorMsg = `Syntax error in ${filePath}: ${error.message}`;
      this.validationResults.syntax.errors.push(errorMsg);
      this.log(`❌ ${errorMsg}`);
    }
  }

  /**
   * Validate that tests are working
   */
  async validateTests() {
    this.log('🔍 Validating tests...');
    
    try {
      // Check if test command exists in package.json
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const testScript = packageJson.scripts?.test;
      
      if (!testScript) {
        this.validationResults.tests.failed++;
        this.validationResults.tests.errors.push('No test script found in package.json');
        this.log('❌ No test script found in package.json');
        return;
      }

      // Run tests
      const result = execSync('npm test', { 
        stdio: 'pipe',
        encoding: 'utf8',
        timeout: 60000 // 1 minute timeout
      });
      
      this.validationResults.tests.passed++;
      this.log('✅ Tests passed');
    } catch (error) {
      this.validationResults.tests.failed++;
      const errorMsg = `Tests failed: ${error.stdout || error.stderr || error.message}`;
      this.validationResults.tests.errors.push(errorMsg);
      this.log(`❌ ${errorMsg}`);
    }
  }

  /**
   * Validate that build process works
   */
  async validateBuild() {
    this.log('🔍 Validating build process...');
    
    try {
      // Check if build command exists in package.json
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const buildScript = packageJson.scripts?.build;
      
      if (!buildScript) {
        this.validationResults.build.failed++;
        this.validationResults.build.errors.push('No build script found in package.json');
        this.log('❌ No build script found in package.json');
        return;
      }

      // Run build
      const result = execSync('npm run build', { 
        stdio: 'pipe',
        encoding: 'utf8',
        timeout: 300000 // 5 minute timeout
      });
      
      this.validationResults.build.passed++;
      this.log('✅ Build successful');
    } catch (error) {
      this.validationResults.build.failed++;
      const errorMsg = `Build failed: ${error.stdout || error.stderr || error.message}`;
      this.validationResults.build.errors.push(errorMsg);
      this.log(`❌ ${errorMsg}`);
    }
  }

  /**
   * Run comprehensive validation
   */
  async runFullValidation() {
    this.log('🚀 Starting comprehensive validation...');
    
    this.validateFolderStructure();
    await this.validateImports();
    await this.validateSyntax();
    await this.validateTests();
    await this.validateBuild();
    
    this.generateValidationReport();
    
    const totalPassed = Object.values(this.validationResults).reduce((sum, result) => sum + result.passed, 0);
    const totalFailed = Object.values(this.validationResults).reduce((sum, result) => sum + result.failed, 0);
    
    this.log(`✅ Validation complete: ${totalPassed} passed, ${totalFailed} failed`);
    
    return totalFailed === 0;
  }

  /**
   * Generate validation report
   */
  generateValidationReport() {
    const reportPath = path.join(process.cwd(), 'validation-report.md');
    
    const reportContent = `# Migration Validation Report

Generated: ${new Date().toISOString()}

## Summary

| Category | Passed | Failed | Success Rate |
|----------|--------|--------|--------------|
| Structure | ${this.validationResults.structure.passed} | ${this.validationResults.structure.failed} | ${this.calculateSuccessRate('structure')}% |
| Imports | ${this.validationResults.imports.passed} | ${this.validationResults.imports.failed} | ${this.calculateSuccessRate('imports')}% |
| Syntax | ${this.validationResults.syntax.passed} | ${this.validationResults.syntax.failed} | ${this.calculateSuccessRate('syntax')}% |
| Tests | ${this.validationResults.tests.passed} | ${this.validationResults.tests.failed} | ${this.calculateSuccessRate('tests')}% |
| Build | ${this.validationResults.build.passed} | ${this.validationResults.build.failed} | ${this.calculateSuccessRate('build')}% |

## Detailed Results

${Object.entries(this.validationResults).map(([category, results]) => `
### ${category.charAt(0).toUpperCase() + category.slice(1)} Validation

**Status**: ${results.failed === 0 ? '✅ PASSED' : '❌ FAILED'}

${results.errors.length > 0 ? `
**Errors:**
${results.errors.map(error => `- ${error}`).join('\n')}
` : 'No errors found.'}
`).join('\n')}

## Recommendations

${this.generateRecommendations()}
`;

    if (!this.dryRun) {
      try {
        fs.writeFileSync(reportPath, reportContent);
        this.log(`✅ Generated validation report: ${reportPath}`);
      } catch (error) {
        this.log(`❌ Failed to generate validation report: ${error.message}`);
      }
    } else {
      this.log('📊 [DRY RUN] Would generate validation report');
    }
  }

  calculateSuccessRate(category) {
    const results = this.validationResults[category];
    const total = results.passed + results.failed;
    return total === 0 ? 100 : Math.round((results.passed / total) * 100);
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (this.validationResults.structure.failed > 0) {
      recommendations.push('- Create missing folders and index files');
      recommendations.push('- Run Phase 1 to set up proper folder structure');
    }
    
    if (this.validationResults.imports.failed > 0) {
      recommendations.push('- Fix broken import statements');
      recommendations.push('- Run import-updater.js to automatically fix imports');
      recommendations.push('- Check for circular dependencies');
    }
    
    if (this.validationResults.syntax.failed > 0) {
      recommendations.push('- Fix TypeScript/JavaScript syntax errors');
      recommendations.push('- Update type definitions');
      recommendations.push('- Run ESLint to identify and fix issues');
    }
    
    if (this.validationResults.tests.failed > 0) {
      recommendations.push('- Update test imports and dependencies');
      recommendations.push('- Fix failing test cases');
      recommendations.push('- Consider running Phase 6 to add missing tests');
    }
    
    if (this.validationResults.build.failed > 0) {
      recommendations.push('- Fix build configuration');
      recommendations.push('- Update build dependencies');
      recommendations.push('- Check for missing environment variables');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('- All validations passed! Migration appears successful.');
      recommendations.push('- Consider running additional integration tests.');
    }
    
    return recommendations.join('\n');
  }

  /**
   * Rollback to a specific phase or commit
   */
  async rollbackToPhase(phaseNumber) {
    this.log(`🔄 Rolling back to before Phase ${phaseNumber}...`);
    
    if (this.dryRun) {
      this.log(`📝 [DRY RUN] Would rollback to Phase ${phaseNumber}`);
      return;
    }

    try {
      // Look for migration state file
      const stateFile = path.join(process.cwd(), 'migration-state.json');
      
      if (!fs.existsSync(stateFile)) {
        throw new Error('No migration state file found. Cannot perform rollback.');
      }

      const state = JSON.parse(fs.readFileSync(stateFile, 'utf8'));
      const backup = state.backups?.find(b => b.phase === phaseNumber);
      
      if (!backup) {
        throw new Error(`No backup found for Phase ${phaseNumber}`);
      }

      // Read commit hash from backup
      const commitHashFile = path.join(backup.backup, 'commit-hash.txt');
      
      if (fs.existsSync(commitHashFile)) {
        const commitHash = fs.readFileSync(commitHashFile, 'utf8').trim();
        execSync(`git reset --hard ${commitHash}`);
        this.log(`✅ Rolled back to commit ${commitHash}`);
      } else {
        throw new Error('No commit hash found in backup');
      }

      // Update migration state
      state.completedPhases = state.completedPhases.filter(p => p < phaseNumber);
      state.currentPhase = Math.max(0, phaseNumber - 1);
      fs.writeFileSync(stateFile, JSON.stringify(state, null, 2));

      this.log(`✅ Successfully rolled back to before Phase ${phaseNumber}`);
    } catch (error) {
      this.log(`❌ Rollback failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Emergency rollback to initial state
   */
  async emergencyRollback() {
    this.log('🚨 Performing emergency rollback...');
    
    if (this.dryRun) {
      this.log('📝 [DRY RUN] Would perform emergency rollback');
      return;
    }

    try {
      // Try to find the initial commit or stash
      const gitLog = execSync('git log --oneline -10', { encoding: 'utf8' });
      const lines = gitLog.split('\n');
      
      // Look for a commit before migration started
      const preMigrationCommit = lines.find(line => 
        line.includes('before migration') || 
        line.includes('pre-migration') ||
        line.includes('initial')
      );

      if (preMigrationCommit) {
        const commitHash = preMigrationCommit.split(' ')[0];
        execSync(`git reset --hard ${commitHash}`);
        this.log(`✅ Emergency rollback to commit ${commitHash}`);
      } else {
        // Fallback to git stash if available
        try {
          execSync('git stash pop');
          this.log('✅ Emergency rollback using git stash');
        } catch (stashError) {
          throw new Error('No suitable rollback point found. Manual intervention required.');
        }
      }

      // Clean up migration files
      const migrationFiles = [
        'migration-state.json',
        'migration.log',
        'validation-report.md',
        'import-update-report.md'
      ];

      for (const file of migrationFiles) {
        const filePath = path.join(process.cwd(), file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          this.log(`🗑️  Cleaned up: ${file}`);
        }
      }

      this.log('✅ Emergency rollback completed');
    } catch (error) {
      this.log(`❌ Emergency rollback failed: ${error.message}`);
      throw error;
    }
  }

  // Helper methods
  findSourceFiles() {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const ignorePaths = ['node_modules', '.git', 'dist', 'build', 'coverage', 'migration-scripts'];
    
    const findFiles = (dir) => {
      const files = [];
      
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !ignorePaths.includes(item)) {
            files.push(...findFiles(fullPath));
          } else if (stat.isFile() && extensions.includes(path.extname(item))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
      
      return files;
    };
    
    return findFiles(process.cwd());
  }

  extractImports(content) {
    const imports = [];
    const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push({
        statement: match[0],
        path: match[1]
      });
    }
    
    return imports;
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const options = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose')
  };

  const manager = new ValidationRollbackManager(options);

  (async () => {
    try {
      switch (command) {
        case 'validate':
          await manager.runFullValidation();
          break;

        case 'rollback':
          const phaseNumber = parseInt(args[1]);
          if (isNaN(phaseNumber)) {
            throw new Error('Please specify a phase number to rollback to');
          }
          await manager.rollbackToPhase(phaseNumber);
          break;

        case 'emergency-rollback':
          await manager.emergencyRollback();
          break;

        case 'validate-structure':
          manager.validateFolderStructure();
          break;

        case 'validate-imports':
          await manager.validateImports();
          break;

        case 'validate-syntax':
          await manager.validateSyntax();
          break;

        case 'validate-tests':
          await manager.validateTests();
          break;

        case 'validate-build':
          await manager.validateBuild();
          break;

        default:
          console.log(`
🔍 Validation and Rollback Manager

Usage:
  node validation-rollback.js <command> [options]

Commands:
  validate                 Run full validation suite
  validate-structure       Validate folder structure only
  validate-imports         Validate import statements only
  validate-syntax          Validate syntax only
  validate-tests          Validate tests only
  validate-build          Validate build process only
  rollback <phase>        Rollback to before specified phase
  emergency-rollback      Emergency rollback to initial state

Options:
  --dry-run               Run without making changes
  --verbose               Show detailed output

Examples:
  node validation-rollback.js validate
  node validation-rollback.js rollback 3
  node validation-rollback.js emergency-rollback
  node validation-rollback.js validate --verbose
          `);
          break;
      }
    } catch (error) {
      console.error('❌ Error:', error.message);
      process.exit(1);
    }
  })();
}

module.exports = ValidationRollbackManager;