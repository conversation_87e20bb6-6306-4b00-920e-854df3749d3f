#!/usr/bin/env node

/**
 * Migration Orchestrator for Architectural Improvement
 * Manages the 6-phase migration plan with safety checks and rollback capabilities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class MigrationOrchestrator {
  constructor() {
    this.phases = [
      {
        id: 1,
        name: 'Create New Folder Structure',
        script: './migration-scripts/phase1-folders.cjs',
      },
      { id: 2, name: 'Move Utility Functions', script: './migration-scripts/phase2-utilities.cjs' },
      {
        id: 3,
        name: 'Extract and Move Custom Hooks',
        script: './migration-scripts/phase3-hooks.cjs',
      },
      {
        id: 4,
        name: 'Split Large Components',
        script: './migration-scripts/phase4-components.cjs',
      },
      { id: 5, name: 'Reorganize Components', script: './migration-scripts/phase5-reorganize.cjs' },
      { id: 6, name: 'Add Missing Tests', script: './migration-scripts/phase6-tests.cjs' },
    ];
    this.logFile = path.join(process.cwd(), 'migration.log');
    this.stateFile = path.join(process.cwd(), 'migration-state.json');
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    console.log(message);
    fs.appendFileSync(this.logFile, logEntry);
  }

  loadState() {
    if (fs.existsSync(this.stateFile)) {
      return JSON.parse(fs.readFileSync(this.stateFile, 'utf8'));
    }
    return {
      currentPhase: 0,
      completedPhases: [],
      failedPhases: [],
      backups: [],
    };
  }

  saveState(state) {
    fs.writeFileSync(this.stateFile, JSON.stringify(state, null, 2));
  }

  createBackup(phaseName) {
    // Sanitize phase name for directory/file names
    const sanitizedPhaseName = phaseName.replace(/\s+/g, '-').replace(/[^a-zA-Z0-9-]/g, '');
    const backupDir = path.join(
      process.cwd(),
      'migration-backups',
      `phase-${sanitizedPhaseName}-${Date.now()}`
    );
    fs.mkdirSync(backupDir, { recursive: true });

    try {
      execSync(`git stash push -m "Pre-${sanitizedPhaseName} backup"`);
      const commitHashFile = path.join(backupDir, 'commit-hash.txt');
      execSync(`git rev-parse HEAD > "${commitHashFile}"`);
      this.log(`✅ Created backup for ${phaseName} at ${backupDir}`);
      return backupDir;
    } catch (error) {
      this.log(`❌ Failed to create backup for ${phaseName}: ${error.message}`);
      throw error;
    }
  }

  validatePrerequisites() {
    this.log('🔍 Validating prerequisites...');

    // Check if git repo is clean (excluding migration-related files)
    try {
      const status = execSync('git status --porcelain', { encoding: 'utf8' });
      if (status.trim()) {
        // Filter out migration-related files that are safe to ignore
        const migrationFiles = [
          'migration.log',
          'migration-state.json',
          'migration-backups/',
          'validation-report.md',
          'import-update-report.md',
          'phase5-migration-report.md',
          'phase6-test-report.md',
        ];

        const statusLines = status.trim().split('\n');
        const problematicChanges = statusLines.filter((line) => {
          // Git status format: XY filename (where X and Y are status codes)
          // Skip the first 2 characters (status codes) and trim
          const filePath = line.substring(2).trim();
          const isIgnored = migrationFiles.some(
            (migrationFile) =>
              filePath === migrationFile ||
              filePath.includes(migrationFile) ||
              filePath.startsWith(migrationFile)
          );
          return !isIgnored;
        });

        if (problematicChanges.length > 0) {
          this.log('⚠️  Found uncommitted changes:');
          problematicChanges.forEach((change) => this.log(`    ${change}`));
          throw new Error(
            'Git repository has uncommitted changes. Please commit or stash changes before migration.\n' +
              'Migration-related files (migration.log, migration-state.json, etc.) are automatically ignored.'
          );
        } else {
          this.log('✅ Git repository is clean (migration files ignored)');
        }
      }
    } catch (error) {
      if (error.message.includes('Git repository has uncommitted changes')) {
        throw error; // Re-throw our custom error
      }
      throw new Error(`Git validation failed: ${error.message}`);
    }

    // Check if all phase scripts exist
    for (const phase of this.phases) {
      if (!fs.existsSync(phase.script)) {
        throw new Error(`Phase script not found: ${phase.script}`);
      }
    }

    // Check if Node.js version supports required features
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 14) {
      throw new Error(
        `Node.js version ${nodeVersion} is not supported. Please use Node.js 14 or higher.`
      );
    }

    this.log('✅ All prerequisites validated');
  }

  async runPhase(phaseId, dryRun = false) {
    const phase = this.phases.find((p) => p.id === phaseId);
    if (!phase) {
      throw new Error(`Phase ${phaseId} not found`);
    }

    this.log(`🚀 Starting ${dryRun ? 'DRY RUN of ' : ''}Phase ${phase.id}: ${phase.name}`);

    const state = this.loadState();

    if (!dryRun) {
      const backupDir = this.createBackup(phase.name);
      state.backups.push({
        phase: phaseId,
        backup: backupDir,
        timestamp: new Date().toISOString(),
      });
    }

    try {
      const args = dryRun ? ['--dry-run'] : [];
      execSync(`node ${phase.script} ${args.join(' ')}`, { stdio: 'inherit' });

      if (!dryRun) {
        state.completedPhases.push(phaseId);
        state.currentPhase = phaseId + 1;
      }

      this.log(
        `✅ ${dryRun ? 'DRY RUN COMPLETED for ' : 'COMPLETED '}Phase ${phase.id}: ${phase.name}`
      );
    } catch (error) {
      if (!dryRun) {
        state.failedPhases.push({
          phase: phaseId,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
      this.log(
        `❌ ${dryRun ? 'DRY RUN FAILED for ' : 'FAILED '}Phase ${phase.id}: ${phase.name} - ${
          error.message
        }`
      );
      throw error;
    } finally {
      if (!dryRun) {
        this.saveState(state);
      }
    }
  }

  async rollbackPhase(phaseId) {
    this.log(`🔄 Rolling back Phase ${phaseId}...`);

    const state = this.loadState();
    const backup = state.backups.find((b) => b.phase === phaseId);

    if (!backup) {
      throw new Error(`No backup found for Phase ${phaseId}`);
    }

    try {
      // Read the commit hash from backup
      const commitHashFile = path.join(backup.backup, 'commit-hash.txt');
      if (fs.existsSync(commitHashFile)) {
        const commitHash = fs.readFileSync(commitHashFile, 'utf8').trim();
        execSync(`git reset --hard ${commitHash}`);
        this.log(`✅ Rolled back to commit ${commitHash}`);
      } else {
        // Fallback to git stash
        execSync('git stash pop');
        this.log('✅ Restored from git stash');
      }

      // Update state
      state.completedPhases = state.completedPhases.filter((p) => p !== phaseId);
      state.failedPhases = state.failedPhases.filter((p) => p.phase !== phaseId);
      state.currentPhase = Math.max(0, Math.min(...state.completedPhases, phaseId - 1));
      this.saveState(state);

      this.log(`✅ Successfully rolled back Phase ${phaseId}`);
    } catch (error) {
      this.log(`❌ Failed to rollback Phase ${phaseId}: ${error.message}`);
      throw error;
    }
  }

  async runAllPhases(options = {}) {
    const { dryRun = false, startFrom = 1, stopAt = 6 } = options;

    this.log(`🎯 Starting migration: Phases ${startFrom}-${stopAt} ${dryRun ? '(DRY RUN)' : ''}`);

    if (!dryRun) {
      this.validatePrerequisites();
    }

    const state = this.loadState();

    for (let i = startFrom; i <= stopAt; i++) {
      if (!dryRun && state.completedPhases.includes(i)) {
        this.log(`⏭️  Skipping Phase ${i} (already completed)`);
        continue;
      }

      try {
        await this.runPhase(i, dryRun);
      } catch (error) {
        this.log(`💥 Migration stopped at Phase ${i} due to error`);
        if (!dryRun) {
          this.log('🔄 Consider running: node migration-orchestrator.js rollback ' + i);
        }
        process.exit(1);
      }
    }

    this.log(`🎉 Migration ${dryRun ? 'dry run ' : ''}completed successfully!`);
  }

  displayStatus() {
    const state = this.loadState();

    console.log('\n📊 MIGRATION STATUS');
    console.log('='.repeat(50));
    console.log(`Current Phase: ${state.currentPhase}`);
    console.log(`Completed Phases: [${state.completedPhases.join(', ')}]`);
    console.log(`Failed Phases: ${state.failedPhases.length}`);
    console.log(`Available Backups: ${state.backups.length}`);

    if (state.failedPhases.length > 0) {
      console.log('\n❌ FAILED PHASES:');
      state.failedPhases.forEach((failure) => {
        console.log(`  Phase ${failure.phase}: ${failure.error} (${failure.timestamp})`);
      });
    }

    if (state.backups.length > 0) {
      console.log('\n💾 AVAILABLE BACKUPS:');
      state.backups.forEach((backup) => {
        console.log(`  Phase ${backup.phase}: ${backup.backup} (${backup.timestamp})`);
      });
    }
  }
}

// CLI Interface
if (require.main === module) {
  const orchestrator = new MigrationOrchestrator();
  const args = process.argv.slice(2);
  const command = args[0];

  (async () => {
    try {
      switch (command) {
        case 'run':
          const phaseId = parseInt(args[1]);
          const dryRun = args.includes('--dry-run');
          await orchestrator.runPhase(phaseId, dryRun);
          break;

        case 'run-all':
          const options = {
            dryRun: args.includes('--dry-run'),
            startFrom:
              parseInt(args.find((arg) => arg.startsWith('--start-from='))?.split('=')[1]) || 1,
            stopAt: parseInt(args.find((arg) => arg.startsWith('--stop-at='))?.split('=')[1]) || 6,
          };
          await orchestrator.runAllPhases(options);
          break;

        case 'rollback':
          const rollbackPhase = parseInt(args[1]);
          await orchestrator.rollbackPhase(rollbackPhase);
          break;

        case 'status':
          orchestrator.displayStatus();
          break;

        case 'validate':
          orchestrator.validatePrerequisites();
          console.log('✅ All prerequisites validated');
          break;

        default:
          console.log(`
🏗️  Migration Orchestrator

Usage:
  node migration-orchestrator.js <command> [options]

Commands:
  run <phase>              Run a specific phase (1-6)
  run-all                  Run all phases
  rollback <phase>         Rollback a specific phase
  status                   Show migration status
  validate                 Validate prerequisites

Options:
  --dry-run               Run without making changes
  --start-from=<phase>    Start from specific phase (for run-all)
  --stop-at=<phase>       Stop at specific phase (for run-all)

Examples:
  node migration-orchestrator.js run 1 --dry-run
  node migration-orchestrator.js run-all --start-from=2 --stop-at=4
  node migration-orchestrator.js rollback 3
  node migration-orchestrator.js status
          `);
          break;
      }
    } catch (error) {
      console.error('❌ Error:', error.message);
      process.exit(1);
    }
  })();
}

module.exports = MigrationOrchestrator;
